// app.js
// Node.js port of the multi-casino, multi-language promotion scraper

// =========================
// Imports
// =========================
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const schedule = require("node-schedule");
const cheerio = require("cheerio");
const mysql = require("mysql2/promise");
const { Builder, By } = require("selenium-webdriver");
const chrome = require("selenium-webdriver/chrome");
const { URL } = require("url");


// =========================
/** Database Configuration (XAMPP local) */
// =========================
const DB_CONFIG = {
  host: "localhost",
  database: "knowledge", // Fixed DB name
  user: "phpmyadmin",
  password: "D6UzI1C3AM@QU9aKNlPb", // Default XAMPP MySQL password is empty
  port: 3306,
};

// =========================
/** Globals / Misc */
// =========================
const first_sch_hour = 8; // 08:00 AM
const first_sch_minute = 0;
const second_sch_hour = 13; // 05:00 PM
const second_sch_minute = 16;
const SCHEDULE_TIMES = [
  `${String(first_sch_hour).padStart(2, "0")}:${String(
    first_sch_minute
  ).padStart(2, "0")}`,
  `${String(second_sch_hour).padStart(2, "0")}:${String(
    second_sch_minute
  ).padStart(2, "0")}`,
];

const CHANGE_LOG_FILE = path.join(process.cwd(), "all_casinos_changes.log");

// =========================
/** DB Helpers */
// =========================
async function connectToDatabase() {
  try {
    const conn = await mysql.createConnection(DB_CONFIG);
    console.log("✅ Connected to MySQL database");
    return conn;
  } catch (e) {
    console.error("❌ Error connecting to MySQL database:", e.message);
    return null;
  }
}

// =========================
/** Casino Brand Loader */
// =========================
async function fetchCasinoBrands() {
  const connection = await connectToDatabase();
  if (!connection) return {};

  try {
    const [tablesRes] = await connection.query(`SHOW TABLES`);
    const tables = tablesRes.map((row) => Object.values(row)[0]);
    console.log(
      `📋 Available tables: ${tables.length ? tables.join(", ") : "(none)"}`
    );

    const casinoSites = {};
    if (tables.includes("brands")) {
      console.log("🔍 Fetching from brands table only...");
      const [brands] = await connection.query(
        `SELECT * FROM brands WHERE active = 1`
      );
      console.log(`📋 Found ${brands.length} active brands in brands table`);

      for (const brand of brands) {
        const brand_id = brand?.id ?? "unknown";
        const brand_name = brand?.name || "Unknown Casino";
        const main_url = brand?.url || "";
        const language = brand?.language || "English";
        const lang_code = brand?.langcode || "en";
        const active_status = String(brand?.active ?? "").trim();

        if (active_status === "1" && main_url) {
          let brand_url = main_url;
          if (
            !/\/promotions($|\/|\?)/i.test(main_url) &&
            !/\/events\/promotions/i.test(main_url)
          ) {
            brand_url = main_url.replace(/\/+$/, "") + "/events/promotions";
          }

          let brand_key = String(brand_name)
            .toLowerCase()
            .replace(/\s+/g, "")
            .replace(/casino|[-_]/g, "");
          if (!brand_key) brand_key = `casino_${brand_id}`;

          casinoSites[brand_key] = {
            url: brand_url,
            name: brand_name,
            language,
            lang_code,
            hash_file: `${brand_key}_hash.txt`,
          };
          console.log(
            `📌 Added from brands: ${brand_name} (${language}, ${lang_code}) - ${brand_url}`
          );
        } else {
          console.log(`⚠  Skipped ${brand_name}: Not active or no URL found`);
        }
      }
    } else {
      console.log("❌ Brands table not found!");
    }

    console.log(
      `✅ Total casinos configured: ${Object.keys(casinoSites).length}`
    );
    await connection.end();
    return casinoSites;
  } catch (e) {
    console.error("❌ Error fetching casino data:", e.message);
    console.log("💡 Please check your database tables structure");
    return {};
  } finally {
    if (connection) await connection.end().catch(() => {});
  }
}

// =========================
/** WebDriver Setup */
// =========================
// More robust setupDriver function with aggressive cleanup
// Alternative approach - don't use user-data-dir at all
async function setupDriver() {
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);

  // Kill any existing Chrome processes (Windows compatible)
  try {
    console.log("🔧 Ensuring no Chrome processes are running...");
    // Use Windows taskkill command instead of pkill
    await execAsync('taskkill /F /IM chrome.exe /T 2>nul || echo "No Chrome processes found"');
    await execAsync('taskkill /F /IM chromedriver.exe /T 2>nul || echo "No ChromeDriver processes found"');
    await sleep(3000); // Longer wait
  } catch (e) {
    console.log("Chrome process cleanup completed");
  }
  
  // Create a unique temporary directory for this session
  const os = require('os');
  const tempDir = path.join(os.tmpdir(), `chrome-user-data-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);

  const options = new chrome.Options().addArguments(
    "--headless=new",
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-software-rasterizer",
    "--window-size=1920,1080",
    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "--disable-logging",
    "--disable-extensions",
    "--disable-background-networking",
    "--disable-background-timer-throttling",
    "--disable-client-side-phishing-detection",
    "--disable-default-apps",
    "--disable-hang-monitor",
    "--disable-prompt-on-repost",
    "--disable-sync",
    "--log-level=3",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor,TranslateUI",
    "--disable-setuid-sandbox",
    "--disable-background-mode",
    "--disable-renderer-backgrounding",
    "--remote-debugging-port=0",
    "--disable-ipc-flooding-protection",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-popup-blocking",
    "--disable-blink-features=AutomationControlled",
    "--disable-infobars",
    "--disable-notifications",
    `--user-data-dir=${tempDir}`, // Use unique temp directory
    "--single-process" // This helps prevent multiple Chrome instances
  );

  console.log("🚀 Initializing Chrome with unique user data directory...");
  console.log(`📁 Temp directory: ${tempDir}`);

  const driver = await new Builder()
    .forBrowser("chrome")
    .setChromeOptions(options)
    .build();

  // Store temp directory on driver for cleanup later
  driver._tempDir = tempDir;

  console.log("✅ Chrome WebDriver initialized successfully");
  return driver;
}

// =========================
/** Content Hash Helpers (kept) */
// =========================
function calculateContentHash(promotions) {
  let content = "";
  for (const p of promotions) {
    content += p?.title_original || "";
    content += p?.description_original || "";
    content += p?.detail_link || "";

    const inner = p?.promotion_content_original || {};
    for (const v of Object.values(inner)) {
      if (Array.isArray(v)) content += v.join("");
      else content += String(v ?? "");
    }

    const innerEn = p?.promotion_content_en || {};
    for (const v of Object.values(innerEn)) {
      if (Array.isArray(v)) content += v.join("");
      else content += String(v ?? "");
    }
  }
  return crypto.createHash("sha256").update(content, "utf8").digest("hex");
}

function loadPreviousHash(hashFile) {
  try {
    if (fs.existsSync(hashFile)) {
      return fs.readFileSync(hashFile, "utf8").trim();
    }
  } catch (e) {
    console.error("Error loading previous hash:", e.message);
  }
  return null;
}

function saveCurrentHash(contentHash, hashFile) {
  try {
    fs.writeFileSync(hashFile, contentHash, "utf8");
  } catch (e) {
    console.error("Error saving hash:", e.message);
  }
}

function logChangeDetection(hasChanges, timestamp, casinoName, details = "") {
  try {
    const status = hasChanges ? "CHANGES DETECTED" : "NO CHANGES";
    fs.appendFileSync(
      CHANGE_LOG_FILE,
      `[${timestamp}] ${casinoName} - ${status} - ${details}\n`,
      "utf8"
    );
  } catch (e) {
    console.error("Error logging changes:", e.message);
  }
}

// =========================
/** Scrapers */
// =========================
async function sleep(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

function addLangParam(url, lang) {
  if (!lang) return url;
  return url.includes("?") ? `${url}&lang=${lang}` : `${url}?lang=${lang}`;
}

async function scrape_promotion_list(
  driver,
  url,
  casinoName,
  { isEnglish = false, langCode = "en" } = {}
) {
  console.log(
    `🎰 Loading ${casinoName} promotions ${
      isEnglish ? "(English)" : "(Original)"
    }`
  );
  const scrapeUrl = addLangParam(url, isEnglish ? "en" : langCode);
  console.log(`🔗 URL: ${scrapeUrl}`);

  try {
    await driver.get(scrapeUrl);
    await sleep(10000);

    await driver.executeScript(
      "window.scrollTo(0, document.body.scrollHeight);"
    );
    await sleep(3000);
    await driver.executeScript("window.scrollTo(0, 0);");
    await sleep(2000);

    const page = await driver.getPageSource();
    const $ = cheerio.load(page);

    const selectors = [
      ".promotion-card",
      ".promo-card",
      ".bonus-card",
      ".kampanya-card",
      ".promotion-item",
      ".promo-item",
      ".bonus-item",
      ".kampanya-item",
      ".promotion",
      ".promo",
      ".bonus",
      ".kampanya",
      '[class*="promotion"]',
      '[class*="promo"]',
      '[class*="bonus"]',
      '[class*="kampanya"]',
    ];

    let promotionCards = [];
    for (const sel of selectors) {
      const found = $(sel).toArray();
      if (found.length) {
        promotionCards = found;
        console.log(
          `✅ Found ${promotionCards.length} promotion cards with selector: ${sel}`
        );
        break;
      }
    }

    if (!promotionCards.length) {
      console.log(
        `⚠  No promotion cards found on ${casinoName} - trying fallback`
      );
      promotionCards = $(
        'div[class*="card"], .card, article, .content-item'
      ).toArray();
      console.log(
        `📋 Found ${promotionCards.length} potential promotion elements as fallback`
      );
    }

    const promotions = [];
    promotionCards.forEach((card, idx) => {
      const node = $(card);
      const promotion = {
        number: idx + 1,
        title_original: "",
        description_original: "",
        detail_link: "",
        image_url: "",
        promotion_content_original: {},
        promotion_content_en: {},
      };

      // Titles
      const titleSelectors = [
        ".banner-title",
        ".title",
        ".promo-title",
        ".promotion-title",
        ".card-title",
        ".heading",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        '[class*="title"]',
        '[class*="heading"]',
      ];
      for (const tsel of titleSelectors) {
        const elem = node.find(tsel).first();
        if (elem && elem.text().trim()) {
          promotion.title_original = elem.text().trim();
          break;
        }
      }
      if (!promotion.title_original) {
        promotion.title_original = `Promotion ${idx + 1}`;
      }

      // Descriptions
      const descriptionSelectors = [
        ".banner-subtitle",
        ".subtitle",
        ".description",
        ".promo-description",
        ".card-text",
        ".content",
        ".summary",
        "p",
        ".text",
        '[class*="subtitle"]',
        '[class*="description"]',
        '[class*="text"]',
      ];
      for (const dsel of descriptionSelectors) {
        const elems = node.find(dsel).toArray();
        if (elems.length) {
          const parts = elems.map((e) => $(e).text().trim()).filter(Boolean);
          if (parts.length) {
            promotion.description_original = parts.join(" | ");
            break;
          }
        }
      }

      // Detail links
      const linkSelectors = [
        "[data-link]",
        "a[href]",
        "[href]",
        ".link",
        ".more-link",
        '[class*="link"]',
        "[data-url]",
        "[data-href]",
      ];
      for (const lsel of linkSelectors) {
        const lelem = node.find(lsel).first();
        if (lelem && lelem.length) {
          const link =
            lelem.attr("data-link") ||
            lelem.attr("href") ||
            lelem.attr("data-url") ||
            lelem.attr("data-href") ||
            "";
          if (link) {
            promotion.detail_link = link;
            break;
          }
        }
      }

      // Image
      const img = node.find("img").first();
      if (img && img.length) {
        promotion.image_url = img.attr("src") || img.attr("data-src") || "";
      }

      promotions.push(promotion);
      console.log(
        `📋 Found Promotion ${promotion.number}: ${promotion.title_original}`
      );
    });

    return promotions;
  } catch (e) {
    console.error(`❌ Error scraping ${scrapeUrl}:`, e.message);
    return [];
  }
}

function extract_precise_promotion_content($) {
  const content = {
    main_title: "",
    main_description: "",
    promotion_details: [],
    terms_and_conditions: [],
    how_to_participate: [],
    bonus_information: [],
  };

  $(
    "nav, header, footer, .sidebar, .navigation, .breadcrumb, .social-media, .footer-links, script, style, noscript, .menu, .nav, .header-menu, .footer-menu"
  ).remove();

  const mainSelectors = [
    ".promo-content",
    ".promotion-content",
    ".main-content",
    ".content-area",
    ".promotion-details",
    ".promo-details",
    "main",
    ".container",
    ".promotion-page",
  ];

  let main = null;
  for (const sel of mainSelectors) {
    const m = $(sel).first();
    if (m && m.length) {
      main = m;
      console.log(`✅ Found main content with selector: ${sel}`);
      break;
    }
  }

  if (!main) {
    main = $("body").first();
    if (main && main.length) {
      main
        .find(".header, .nav, .sidebar, .footer, .breadcrumb, .menu")
        .remove();
    }
  }

  if (main && main.length) {
    const titleElem = main
      .find("h1, h2, .main-title, .promotion-title, .page-title, .title")
      .first();
    if (titleElem && titleElem.text()) {
      content.main_title = titleElem.text().trim();
    }

    const descElem = main
      .find(".main-description, .promotion-description, .intro, .description")
      .first();
    if (descElem && descElem.text()) {
      content.main_description = descElem.text().trim();
    }

    const textElems = main
      .find(
        "p, div, span, li, h1, h2, h3, h4, h5, h6, .text, .content, strong, b, em, i"
      )
      .toArray();
    const uniqueTexts = [];

    const commonTemplateTexts = [
      "misty casino",
      "casino",
      "spor",
      "canlı casino",
      "slot",
      "poker",
      "blackjack",
      "giriş",
      "kayıt",
      "üye ol",
      "giriş yap",
      "hesap",
      "profil",
      "ayarlar",
      "yardım",
      "destek",
      "iletişim",
      "hakkımızda",
      "kullanım koşulları",
      "gizlilik politikası",
      "sorumlu oyun",
      "lisans",
      "güvenlik",
      "mobil",
      "android",
      "ios",
      "uygulama",
      "indir",
      "sosyal medya",
      "facebook",
      "twitter",
      "instagram",
      "telegram",
      "whatsapp",
      "copyright",
      "©",
      "tüm hakları saklıdır",
      "all rights reserved",
    ];

    for (const e of textElems) {
      const text = $(e).text().trim();
      const lower = text.toLowerCase();

      const isTemplate = commonTemplateTexts.some((t) => lower.includes(t));
      const isNav = [
        "menu",
        "nav",
        "login",
        "register",
        "home",
        "contact",
        "about",
        "support",
        "help",
      ].includes(lower);
      const isTooShort = text.length < 10;
      const isDup = uniqueTexts.includes(text);

      if (isTemplate || isNav || isTooShort || isDup) continue;

      const hasPromoKeywords = [
        // English
        "bonus",
        "promotion",
        "campaign",
        "gift",
        "win",
        "reward",
        "free",
        "spin",
        "bet",
        "minimum",
        "maximum",
        "terms",
        "conditions",
        "rules",
        "valid",
        "limit",
        "how",
        "participate",
        "apply",
        "get",
        "play",
        "select",
        "enter",
        "date",
        "time",
        "day",
        "hour",
        // Turkish
        "promosyon",
        "kampanya",
        "hediye",
        "kazanç",
        "ödül",
        "çevrim",
        "yatırım",
        "para",
        "₺",
        "tl",
        "bedava",
        "koşul",
        "şart",
        "kural",
        "geçerli",
        "sınır",
        "nasıl",
        "katıl",
        "başvur",
        "al",
        "kazan",
        "oyna",
        "seç",
        "gir",
        "tarih",
        "süre",
        "zaman",
        "gün",
        "saat",
        // Portuguese
        "bônus",
        "promoção",
        "campanha",
        "presente",
        "ganhar",
        "prêmio",
        "grátis",
        "giro",
        "aposta",
        "mínimo",
        "máximo",
        "termos",
        "condições",
        "regras",
        "válido",
        "limite",
        "como",
        "participar",
        "aplicar",
        "obter",
        "jogar",
        "selecionar",
        "entrar",
        "data",
        "tempo",
        "dia",
        "hora",
        "real",
        "r$",
        // Spanish
        "promoción",
        "campaña",
        "regalo",
        "ganar",
        "premio",
        "gratis",
        "giro",
        "apuesta",
        "mínimo",
        "máximo",
        "términos",
        "condiciones",
        "reglas",
        "válido",
        "límite",
        "cómo",
        "participar",
        "aplicar",
        "obtener",
        "jugar",
        "seleccionar",
        "entrar",
        "fecha",
        "tiempo",
        "día",
        "hora",
        "euro",
        "€",
        // Indonesian
        "promosi",
        "kampanye",
        "hadiah",
        "menang",
        "penghargaan",
        "gratis",
        "putar",
        "taruhan",
        "minimum",
        "maksimum",
        "syarat",
        "ketentuan",
        "aturan",
        "berlaku",
        "batas",
        "bagaimana",
        "berpartisipasi",
        "mendaftar",
        "dapatkan",
        "bermain",
        "pilih",
        "masuk",
        "tanggal",
        "waktu",
        "hari",
        "jam",
        "rupiah",
        "rp",
        "%",
        "$",
        "€",
        "₺",
        "r$",
        "rp",
      ].some((k) => lower.includes(k));

      if (hasPromoKeywords) uniqueTexts.push(text);
    }

    const participationKeywords = [
      "nasıl",
      "katıl",
      "başvur",
      "yatır",
      "al",
      "kazan",
      "adım",
      "işlem",
      "yapın",
      "tıklayın",
      "oyna",
      "seç",
      "gir",
      "como",
      "participar",
      "aplicar",
      "depositar",
      "obter",
      "ganhar",
      "passo",
      "processo",
      "fazer",
      "clicar",
      "jogar",
      "selecionar",
      "entrar",
      "cómo",
      "aplicar",
      "depositar",
      "obtener",
      "ganar",
      "paso",
      "proceso",
      "hacer",
      "hacer clic",
      "jugar",
      "seleccionar",
      "entrar",
      "bagaimana",
      "berpartisipasi",
      "mendaftar",
      "deposit",
      "dapatkan",
      "menang",
      "langkah",
      "proses",
      "lakukan",
      "klik",
      "bermain",
      "pilih",
      "masuk",
      "how",
      "participate",
      "apply",
      "deposit",
      "get",
      "win",
      "step",
      "process",
      "do",
      "click",
      "play",
      "select",
      "enter",
    ];
    const termsKeywords = [
      "koşul",
      "şart",
      "kural",
      "geçerli",
      "sınır",
      "çevrim",
      "gün",
      "saat",
      "kez",
      "tarih",
      "süre",
      "zaman",
      "termos",
      "condições",
      "regras",
      "válido",
      "limite",
      "rollover",
      "dia",
      "hora",
      "vez",
      "data",
      "tempo",
      "prazo",
      "términos",
      "condiciones",
      "reglas",
      "válido",
      "límite",
      "rollover",
      "día",
      "hora",
      "vez",
      "fecha",
      "tiempo",
      "plazo",
      "syarat",
      "ketentuan",
      "aturan",
      "berlaku",
      "batas",
      "rollover",
      "hari",
      "jam",
      "kali",
      "tanggal",
      "waktu",
      "jangka",
      "terms",
      "conditions",
      "rules",
      "valid",
      "limit",
      "rollover",
      "day",
      "hour",
      "time",
      "date",
      "period",
      "deadline",
    ];
    const bonusKeywords = [
      "bonus",
      "minimum",
      "maksimum",
      "oran",
      "₺",
      "tl",
      "para",
      "miktar",
      "bedava",
      "free",
      "spin",
      "bet",
      "bônus",
      "mínimo",
      "máximo",
      "taxa",
      "r$",
      "real",
      "dinheiro",
      "quantia",
      "grátis",
      "giro",
      "aposta",
      "bono",
      "mínimo",
      "máximo",
      "tasa",
      "€",
      "euro",
      "dinero",
      "cantidad",
      "gratis",
      "giro",
      "apuesta",
      "tingkat",
      "rp",
      "rupiah",
      "uang",
      "jumlah",
      "gratis",
      "putar",
      "taruhan",
      "$",
      "money",
      "amount",
      "free",
      "spin",
      "bet",
      "%",
      "€",
      "₺",
      "r$",
      "rp",
    ];

    for (const text of uniqueTexts) {
      const lower = text.toLowerCase();
      if (participationKeywords.some((w) => lower.includes(w))) {
        content.how_to_participate.push(text);
      } else if (termsKeywords.some((w) => lower.includes(w))) {
        content.terms_and_conditions.push(text);
      } else if (bonusKeywords.some((w) => lower.includes(w))) {
        content.bonus_information.push(text);
      } else {
        content.promotion_details.push(text);
      }
    }
  }

  return content;
}

async function scrape_promotion_detail(
  driver,
  promotion,
  baseUrl,
  { isEnglish = false, langCode = "en" } = {}
) {
  if (!promotion?.detail_link) return;

  let detailUrl;
  try {
    const base = new URL(baseUrl);
    const baseDomain = `${base.protocol}//${base.host}`;
    const maybe = new URL(promotion.detail_link, baseDomain);
    detailUrl = addLangParam(maybe.toString(), isEnglish ? "en" : langCode);
  } catch {
    detailUrl = addLangParam(
      promotion.detail_link,
      isEnglish ? "en" : langCode
    );
  }

  console.log(
    `🔍 Scraping ${isEnglish ? "English" : "Original"} details: ${detailUrl}`
  );

  try {
    await driver.get(detailUrl);
    await sleep(8000);
    await driver.executeScript(
      "window.scrollTo(0, document.body.scrollHeight);"
    );
    await sleep(3000);
    await driver.executeScript("window.scrollTo(0, 0);");
    await sleep(2000);

    const page = await driver.getPageSource();
    const $ = cheerio.load(page);
    const content = extract_precise_promotion_content($);

    if (isEnglish) {
      promotion.promotion_content_en = content;
    } else {
      promotion.promotion_content_original = content;
    }

    console.log("✅ Extracted precise promotion content:");
    console.log(
      `   📝 Main title: ${(content.main_title || "").slice(0, 50)}...`
    );
    console.log(
      `   📄 Description: ${(content.main_description || "").slice(0, 50)}...`
    );
    console.log(`   🎯 Details: ${content.promotion_details.length} items`);
    console.log(`   ⚖  Terms: ${content.terms_and_conditions.length} items`);
    console.log(`   🎁 Bonus info: ${content.bonus_information.length} items`);
    console.log(
      `   📋 How to participate: ${content.how_to_participate.length} items`
    );
  } catch (e) {
    console.error(`❌ Error scraping ${detailUrl}:`, e.message);
    if (isEnglish) promotion.promotion_content_en = {};
    else promotion.promotion_content_original = {};
  }
}

// =========================
/** Formatting */
// =========================
function format_promotion_content(dict) {
  if (!dict) return "";
  const formatted = [];

  if (dict.title) formatted.push(`TITLE: ${dict.title}`);
  if (dict.description) formatted.push(`DESCRIPTION: ${dict.description}`);

  const c = dict.content || {};
  if (c.main_title) formatted.push(`MAIN TITLE: ${c.main_title}`);
  if (c.main_description)
    formatted.push(`MAIN DESCRIPTION: ${c.main_description}`);

  if (Array.isArray(c.promotion_details) && c.promotion_details.length) {
    formatted.push("PROMOTION DETAILS:");
    c.promotion_details.forEach((d) => formatted.push(`• ${d}`));
  }
  if (Array.isArray(c.how_to_participate) && c.how_to_participate.length) {
    formatted.push("HOW TO PARTICIPATE:");
    c.how_to_participate.forEach((s) => formatted.push(`• ${s}`));
  }
  if (Array.isArray(c.terms_and_conditions) && c.terms_and_conditions.length) {
    formatted.push("TERMS AND CONDITIONS:");
    c.terms_and_conditions.forEach((t) => formatted.push(`• ${t}`));
  }
  if (Array.isArray(c.bonus_information) && c.bonus_information.length) {
    formatted.push("BONUS INFORMATION:");
    c.bonus_information.forEach((b) => formatted.push(`• ${b}`));
  }

  return formatted.join("\n");
}

// =========================
/** DB Writes */
// =========================
async function store_promotions_in_database(
  original_promotions,
  english_promotions,
  casino_name,
  brand_url
) {
  const connection = await connectToDatabase();
  if (!connection) return false;

  try {
    await connection.execute(`DELETE FROM promotions WHERE brand_name = ?`, [
      casino_name,
    ]);

    const total = Math.min(
      original_promotions.length,
      english_promotions.length
    );
    const insertSQL = `
      INSERT INTO promotions (brand_name, brand_url, Source, Target, active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, NOW(), NOW())
    `;
    for (let i = 0; i < total; i++) {
      const orig = original_promotions[i];
      const eng = english_promotions[i];

      const original_content = format_promotion_content({
        title: orig?.title_original || "",
        description: orig?.description_original || "",
        content: orig?.promotion_content_original || {},
      });

      const english_content = format_promotion_content({
        title: eng?.title_original || "",
        description: eng?.description_original || "",
        content: eng?.promotion_content_en || {},
      });

      await connection.execute(insertSQL, [
        casino_name,
        brand_url,
        english_content, // Source = English
        original_content, // Target = Original
        1,
      ]);
    }

    console.log(`✅ Stored ${total} promotions for ${casino_name}`);
    await connection.end();
    return true;
  } catch (e) {
    console.error("❌ Database error:", e.message);
    return false;
  } finally {
    if (connection) await connection.end().catch(() => {});
  }
}

async function batch_update_database(all_promotions) {
  const connection = await connectToDatabase();
  if (!connection) return false;

  try {
    console.log("🗑 Deleting all existing promotions...");
    await connection.execute(`DELETE FROM promotions`);
    await connection.execute(`ALTER TABLE promotions AUTO_INCREMENT = 1`);

    if (all_promotions.length) {
      console.log(`📥 Inserting ${all_promotions.length} new promotions...`);
      const insertSQL = `
        INSERT INTO promotions (brand_name, brand_url, Source, Target, active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `;
      const values = all_promotions.map((p) => [
        p.brand_name,
        p.brand_url,
        p.source,
        p.target,
        1,
      ]);

      // Insert sequentially to avoid packet size/config surprises
      for (const row of values) {
        await connection.execute(insertSQL, row);
      }
    }

    await connection.end();
    console.log("✅ Database updated successfully!");
    return true;
  } catch (e) {
    console.error("❌ Database error:", e.message);
    await connection.rollback?.();
    return false;
  } finally {
    if (connection) await connection.end().catch(() => {});
  }
}

// =========================
/** Single / Batch Handlers */
// =========================
async function check_single_casino(driver, casino_config, timestamp) {
  const casino_name = casino_config.name;
  const url = casino_config.url;
  const lang_code = casino_config.lang_code;

  console.log(`\n🎰 Processing ${casino_name}...`);
  console.log(`🔗 URL: ${url}`);
  console.log(`🌍 Language Code: ${lang_code}`);

  try {
    const original_promotions = await scrape_promotion_list(
      driver,
      url,
      casino_name,
      { isEnglish: false, langCode: lang_code }
    );
    console.log(
      `📊 Found ${original_promotions.length} promotions in original language`
    );

    const english_promotions = await scrape_promotion_list(
      driver,
      url,
      casino_name,
      { isEnglish: true, langCode: "en" }
    );
    console.log(`📊 Found ${english_promotions.length} promotions in English`);

    const total = Math.min(
      original_promotions.length,
      english_promotions.length
    );
    for (let i = 0; i < total; i++) {
      console.log(`🔍 Checking promotion ${i + 1}/${total}`);
      await scrape_promotion_detail(driver, original_promotions[i], url, {
        isEnglish: false,
        langCode: lang_code,
      });
      await sleep(1000);
      await scrape_promotion_detail(driver, english_promotions[i], url, {
        isEnglish: true,
        langCode: "en",
      });
      await sleep(1000);
    }

    await store_promotions_in_database(
      original_promotions,
      english_promotions,
      casino_name,
      url
    );
    return true;
  } catch (e) {
    console.error(`❌ Error processing ${casino_name}:`, e.message);
    return false;
  }
}

async function check_all_casinos(driver, timestamp) {
  console.log("🔄 Starting batch processing of all casino promotions...");
  const all_promotions = [];
  const casino_configs = await fetchCasinoBrands();

  for (const config of Object.values(casino_configs)) {
    try {
      const casino_name = config.name;
      const url = config.url;
      const lang_code = config.lang_code;

      console.log(`\n🎰 Processing ${casino_name}...`);
      console.log(`🔗 URL: ${url}`);
      console.log(`🌍 Language Code: ${lang_code}`);

      const original_promotions = await scrape_promotion_list(
        driver,
        url,
        casino_name,
        { isEnglish: false, langCode: lang_code }
      );
      console.log(
        `📊 Found ${original_promotions.length} promotions in original language`
      );

      const english_promotions = await scrape_promotion_list(
        driver,
        url,
        casino_name,
        { isEnglish: true, langCode: "en" }
      );
      console.log(
        `📊 Found ${english_promotions.length} promotions in English`
      );

      const total = Math.min(
        original_promotions.length,
        english_promotions.length
      );
      for (let i = 0; i < total; i++) {
        console.log(`🔍 Checking promotion ${i + 1}/${total}`);
        await scrape_promotion_detail(driver, original_promotions[i], url, {
          isEnglish: false,
          langCode: lang_code,
        });
        await sleep(1000);
        await scrape_promotion_detail(driver, english_promotions[i], url, {
          isEnglish: true,
          langCode: "en",
        });
        await sleep(1000);

        all_promotions.push({
          brand_name: casino_name,
          brand_url: url,
          source: format_promotion_content({
            title: english_promotions[i]?.title_original || "",
            description: english_promotions[i]?.description_original || "",
            content: english_promotions[i]?.promotion_content_en || {},
          }),
          target: format_promotion_content({
            title: original_promotions[i]?.title_original || "",
            description: original_promotions[i]?.description_original || "",
            content: original_promotions[i]?.promotion_content_original || {},
          }),
        });
      }
    } catch (e) {
      console.error(`❌ Error processing ${config.name}:`, e.message);
      continue;
    }
  }

  return all_promotions;
}

// =========================
/** Orchestration */
// =========================
async function check_for_changes() {
  const timestamp = new Date();
  console.log(`\n🕒 Starting check at ${timestamp.toISOString()}`);

  let driver = null;
  try {
    driver = await setupDriver();
    const all = await check_all_casinos(driver, timestamp);

    if (all && all.length) {
      await batch_update_database(all);
      console.log(`✨ Check completed: ${all.length} promotions processed`);
    } else {
      console.log("⚠ No promotions found to process");
    }
  } catch (e) {
    console.error("❌ Error during check:", e.message);
  } finally {
    if (driver) {
      try {
        await driver.quit();
        console.log("🔧 WebDriver closed successfully");
        
        // Small delay to ensure cleanup
        await sleep(1000);
      } catch (e) {
        console.error("Warning: Error closing WebDriver:", e.message);
      }
    }
    
    // Clean up any temporary Chrome directories (Windows compatible)
    try {
      const { exec } = require('child_process');
      // Clean up temp directories on Windows
      exec('for /d %i in ("%TEMP%\\chrome-user-data-*") do rmdir /s /q "%i" 2>nul', () => {});
      exec('for /d %i in ("%LOCALAPPDATA%\\Temp\\chrome-user-data-*") do rmdir /s /q "%i" 2>nul', () => {});
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

function run_scheduler() {
  console.log("⏰ DAILY PROMOTION MONITORING STARTED");
  console.log("📅 Scheduled checks: 08:00 AM and 05:00 PM EVERY DAY");
  console.log("🔄 Behavior:");
  console.log("   • Changes detected → Updates database records");
  console.log("   • No changes → Database records remain unchanged");
  console.log("📊 Next check times:", SCHEDULE_TIMES);

  schedule.scheduleJob(
    { hour: first_sch_hour, minute: first_sch_minute },
    check_for_changes
  );
  console.log("   ⏰ 08:00 AM daily");

  // Schedule #2: 17:00
  schedule.scheduleJob(
    { hour: second_sch_hour, minute: second_sch_minute },
    check_for_changes
  );
  console.log("   ⏰ 5:00 PM daily");

  console.log(
    "\n⚠️ Keep this process running for continuous daily monitoring."
  );
}

async function main() {
  console.log(
    "🚀 Multi-Casino Multi-Language Promotion Auto-Scheduler (Node.js)"
  );
  console.log(
    " Focus: Only actual promotion content (no navigation/menu items)"
  );
  console.log("⏰ Auto-checks: 08:00 AM and 05:00 PM daily");
  console.log(
    "🔄 Updates: Database records with bilingual content when changes detected"
  );
  console.log("🗄️ Data Source: MySQL Database (XAMPP)");
  console.log("=".repeat(100));

  try {
    console.log("\n🔗 Connecting to database...");
    const casino_sites = await fetchCasinoBrands();

    if (!casino_sites || !Object.keys(casino_sites).length) {
      console.log("❌ No casino brands found in database. Please check:");
      console.log("   • XAMPP MySQL server is running");
      console.log("   • Database 'knowledge' exists");
      console.log("   • Table 'brands' exists with data");
      console.log("   • Brands have URLs in 'url' column");
      console.log("   • Database connection settings are correct");
      return;
    }

    console.log("\n⏰ Starting auto-scheduler for daily monitoring...");
    console.log(
      "📅 Schedule: Automatic checks at 08:00 AM and 05:00 PM EVERY DAY"
    );
    console.log("🎰 Casinos Monitored from Database:");
    for (const c of Object.values(casino_sites)) {
      console.log(`   • ${c.name} (${c.language}): ${c.url}`);
    }
    console.log(
      "🔍 Change Detection: Monitors all casino promotion dashboards"
    );
    console.log("� Database Updates:");
    console.log(
      "   ✅ IF CHANGES DETECTED → Updates promotions table with new content"
    );
    console.log("   ✅ IF NO CHANGES → Database records remain unchanged");
    console.log(
      "🌍 Storage: English in Source column, Original language in Target column"
    );
    console.log("⚠️  Keep this terminal open for continuous daily monitoring");
    console.log("\nPress Ctrl+C to stop the daily scheduler\n");

    run_scheduler();
  } catch (e) {
    console.error("❌ Error:", e.message);
  }
}

if (require.main === module) {
  main();
}
